"use client";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@workspace/ui/components/dialog";
import {
  Sheet,
  Sheet<PERSON>rigger,
  She<PERSON><PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
  SheetFooter,
} from "@workspace/ui/components/sheet";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { useRef, useState } from "react";

export default function DialogDemo() {
  const container = useRef<HTMLDivElement>(null);
  const [categoriesOpen, setCategoriesOpen] = useState(false);

  return (
    <Dialog open>
      <DialogContent className="sm:max-w-[700px] overflow-hidden">
        <div
          className="absolute inset-0 overflow-clip rounded-lg"
          ref={container}
        ></div>
        <DialogHeader>
          <DialogTitle>{"Aktivite Seç"}</DialogTitle>
        </DialogHeader>
        <div className="h-96"></div>

        <Sheet open={categoriesOpen} onOpenChange={setCategoriesOpen}>
          <SheetContent
            overlay={false}
            container={container.current ?? undefined}
            side="right"
            className="w-[400px] h-[calc(100%-4rem)] top-1/2 -translate-y-1/2 absolute rounded-lg border-3 border-r-muted-foreground"
          >
            <SheetHeader>
              <SheetTitle>Advanced Profile Settings</SheetTitle>
              <SheetDescription>
                Configure advanced options for your profile.
              </SheetDescription>
            </SheetHeader>

            <SheetFooter>
              <Button>Temizle</Button>
            </SheetFooter>
          </SheetContent>
        </Sheet>
        <DialogFooter>
          <DialogClose asChild>
            <Button>Cancel</Button>
          </DialogClose>
          <Button
            onClick={() => setCategoriesOpen((value) => !value)}
            variant="primary"
            type="submit"
          >
            Save Changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
